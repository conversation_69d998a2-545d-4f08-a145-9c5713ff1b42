interface CustomWindow extends Window {
  updateCodeWrapperMaxWidth?: () => void
  toggleCodeWrap?: (element: HTMLElement) => void
}

declare const window: CustomWindow

function toggleCodeWrap(element: HTMLElement) {
  const hljs = element.closest('.hljs')
  if (!hljs) { return }

  const codeWrapper = hljs.querySelector('.code-wrapper') as HTMLElement
  if (!codeWrapper) { return }

  // 切换到不换行模式
  if (!hljs.classList.contains('code-nowrap')) {
    // 先设置好初始宽度，然后再添加类，避免抖动
    const currentWidth = codeWrapper.getBoundingClientRect().width
    codeWrapper.style.width = `${currentWidth}px`

    // 添加一个延迟，确保宽度已经设置好
    setTimeout(() => {
      hljs.classList.add('code-nowrap')
      if (window.updateCodeWrapperMaxWidth) {
        window.updateCodeWrapperMaxWidth()
      }
    }, 4)
  }
  else {
    // 切换到换行模式
    hljs.classList.remove('code-nowrap')
    // 不立即移除宽度设置，而是以动画方式过渡
    setTimeout(() => {
      codeWrapper.style.width = ''
    }, 50)
  }

  const toggleIcon = element.querySelector('.toggle-wrap-icon')
  if (toggleIcon) {
    toggleIcon.classList.toggle('active')
  }
}

window.toggleCodeWrap = toggleCodeWrap

// PDB 按钮点击处理
document.addEventListener('click', (event) => {
  const target = event.target as HTMLElement
  if (target.classList.contains('juchats-pdb-btn') || target.closest('.juchats-pdb-btn')) {
    const pdbBtn = target.classList.contains('juchats-pdb-btn') ? target : target.closest('.juchats-pdb-btn')
    const wrapper = pdbBtn?.closest('.juchats-pdb-wrapper') as HTMLElement
    if (wrapper) {
      const pdbId = wrapper.getAttribute('data-pdb-id')
      if (pdbId) {
        console.log('PDB ID:', pdbId)
        alert(`Clicked PDB: ${pdbId}`)
      }
    }
  }
})
