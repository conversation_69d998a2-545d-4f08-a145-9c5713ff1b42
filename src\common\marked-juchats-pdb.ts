// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'block',
  start(src: string) {
    return src.match(/<juchats-pdb>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<juchats-pdb>([^<]*)<\/juchats-pdb>/
    const match = rule.exec(src)
    if (match) {
      return {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    console.log('[PDB Debug] Rendering PDB button for:', pdbId)
    return `<p><button
      class="juchats-pdb-btn"
      style="display: inline-flex; align-items: center; padding: 6px 12px; background-color: #3b82f6; color: white; font-size: 14px; border-radius: 6px; cursor: pointer; border: none; margin: 4px;"
      onclick="console.log('PDB ID: ${pdbId}'); alert('Clicked PDB: ${pdbId}');"
      data-pdb-id="${pdbId}"
    >
      <span style="margin-right: 4px;">🧬</span>
      <span>PDB: ${pdbId}</span>
    </button></p>`
  },
}
