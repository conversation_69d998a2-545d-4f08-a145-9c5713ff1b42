// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'inline',
  start(src: string) {
    const match = src.match(/<juchats-pdb>/)
    return match ? match.index : undefined
  },
  tokenizer(src: string) {
    const rule = /^<juchats-pdb>([^<]*)<\/juchats-pdb>/
    const match = rule.exec(src)
    if (match) {
      return {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    return `<button
      class="juchats-pdb-btn inline-flex items-center px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-md cursor-pointer transition-colors duration-200"
      onclick="console.log('PDB ID: ${pdbId}')"
      data-pdb-id="${pdbId}"
    >
      <span class="mr-1">🧬</span>
      <span>PDB: ${pdbId}</span>
    </button>`
  },
}
