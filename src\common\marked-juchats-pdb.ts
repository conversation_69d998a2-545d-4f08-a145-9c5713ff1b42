// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'block',
  start(src: string) {
    return src.match(/<juchats-pdb>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<juchats-pdb>([^<]*)<\/juchats-pdb>/
    const match = rule.exec(src)
    if (match) {
      return {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    // 将 PDB ID 直接嵌入到按钮文本中，避免使用 data 属性
    return `<span class="juchats-pdb-btn" style="display: inline-block; padding: 4px 8px; background: #3b82f6; color: white; border-radius: 4px; cursor: pointer; margin: 2px;">
      🧬 PDB: ${pdbId}
    </span>`
  },
}
