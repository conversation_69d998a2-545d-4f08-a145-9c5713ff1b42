// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'inline',
  start(src: string) {
    return src.match(/<juchats-pdb>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<juchats-pdb>(.*?)(?:<\/juchats-pdb>|$)/
    const match = rule.exec(src)
    if (match) {
      const token = {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
      return token
    }
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    return `<button
      class="juchats-pdb-btn inline-flex items-center px-12px py-4px bg-blue-500 hover:bg-blue-600 text-white text-12px rounded-6px cursor-pointer transition-colors duration-200 border-none outline-none"
      onclick="console.log('PDB ID: ${pdbId}'); alert('PDB ID: ${pdbId}')"
      data-pdb-id="${pdbId}"
    >
      <span class="mr-4px">🧬</span>
      <span>PDB: ${pdbId}</span>
    </button>`
  },
}
