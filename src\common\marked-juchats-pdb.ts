// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'block',
  start(src: string) {
    return src.match(/<juchats-pdb>/)?.index
  },
  tokenizer(src: string) {
    const rule = /^<juchats-pdb>([^<]*)<\/juchats-pdb>/
    const match = rule.exec(src)
    if (match) {
      return {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
    }
    return undefined
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    // 使用更简单的结构，避免复杂的内联样式和事件处理
    return `<span class="juchats-pdb-wrapper" data-pdb-id="${pdbId}">
      <span class="juchats-pdb-btn" style="display: inline-block; padding: 4px 8px; background: #3b82f6; color: white; border-radius: 4px; cursor: pointer; margin: 2px;">
        🧬 PDB: ${pdbId}
      </span>
    </span>`
  },
}
