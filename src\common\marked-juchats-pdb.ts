// @unocss-include
import type { TokenizerAndRendererExtension, Tokens } from 'marked'

export const juchatsPdb: TokenizerAndRendererExtension = {
  name: 'juchatsPdb',
  level: 'block',
  start(src: string) {
    const index = src.match(/<juchats-pdb>/)?.index
    console.log('[PDB Debug] start() called with src:', `${src.substring(0, 100)}...`, 'found index:', index)
    return index
  },
  tokenizer(src: string) {
    console.log('[PDB Debug] tokenizer() called with src:', `${src.substring(0, 100)}...`)
    const rule = /^<juchats-pdb>([^<]*)<\/juchats-pdb>/
    const match = rule.exec(src)
    if (match) {
      console.log('[PDB Debug] tokenizer() matched:', match[0], 'pdbId:', match[1])
      return {
        type: 'juchatsPdb',
        raw: match[0],
        text: match[1].trim(),
      }
    }
    console.log('[PDB Debug] tokenizer() no match, returning undefined')
    return undefined
  },
  renderer(token: Tokens.Generic) {
    const pdbId = token.text
    console.log('[PDB Debug] renderer() called with pdbId:', pdbId)
    return `<p><button
      class="juchats-pdb-btn inline-flex items-center px-3 py-1 bg-blue-500 hover:bg-blue-600 text-white text-sm rounded-md cursor-pointer transition-colors duration-200"
      onclick="console.log('PDB ID: ${pdbId}')"
      data-pdb-id="${pdbId}"
    >
      <span class="mr-1">🧬</span>
      <span>PDB: ${pdbId}</span>
    </button></p>`
  },
}
