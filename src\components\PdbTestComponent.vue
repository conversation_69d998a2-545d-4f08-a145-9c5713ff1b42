<template>
  <div class="p-20px">
    <h2 class="text-24px font-bold mb-16px">PDB 扩展测试</h2>
    
    <div class="mb-20px">
      <h3 class="text-18px font-semibold mb-12px">原始 Markdown 内容：</h3>
      <pre class="bg-gray-100 p-12px rounded-8px text-14px">{{ testMarkdown }}</pre>
    </div>

    <div class="mb-20px">
      <h3 class="text-18px font-semibold mb-12px">渲染后的 HTML：</h3>
      <div class="bg-gray-50 p-12px rounded-8px border" v-html="renderedHtml"></div>
    </div>

    <div class="mb-20px">
      <button 
        class="bg-blue-500 hover:bg-blue-600 text-white px-16px py-8px rounded-6px cursor-pointer"
        @click="testInConsole"
      >
        在控制台测试
      </button>
    </div>

    <div class="text-14px text-gray-600">
      <p>说明：</p>
      <ul class="list-disc pl-20px">
        <li>点击上面的 PDB 按钮应该会在控制台打印 PDB ID 并显示弹窗</li>
        <li>点击"在控制台测试"按钮查看详细的渲染结果</li>
        <li>打开浏览器开发者工具查看控制台输出</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { formatMarkdown } from '@/common/marked'

const testMarkdown = `这是一个测试文档，包含 PDB 标签：

蛋白质结构 <juchats-pdb>1SHR</juchats-pdb> 是一个重要的研究对象。

另一个蛋白质 <juchats-pdb>2ABC</juchats-pdb> 也很有趣。

还有这个结构 <juchats-pdb>3XYZ</juchats-pdb> 值得关注。`

const renderedHtml = computed(() => {
  return formatMarkdown({ string: testMarkdown, receiving: false })
})

const testInConsole = () => {
  console.log('=== PDB 扩展测试 ===')
  console.log('原始 Markdown:', testMarkdown)
  console.log('渲染后的 HTML:', renderedHtml.value)
  console.log('===================')
}

onMounted(() => {
  console.log('PDB 测试组件已加载')
  testInConsole()
})
</script>
